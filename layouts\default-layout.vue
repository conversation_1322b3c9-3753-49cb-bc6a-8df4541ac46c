<template>
  <Menu>
    <MenuButton>Options</MenuButton>
    <MenuItems>
      <MenuItem
        v-for="link in links"
        :key="link.href"
        :href="link.href"
        as="a"
        class="ui-active:bg-blue-500 ui-active:text-white ui-not-active:bg-white ui-not-active:text-black"
      >
        {{ link.label }}
      </MenuItem>
    </MenuItems>
  </Menu>
</template>

<script setup>
  import { Menu, MenuButton, MenuItems, MenuItem } from '@headlessui/vue'

  const links = [
    { href: '/', label: 'Home' },
    { href: '/support', label: 'Support' },
    { href: '/license', label: 'License' },
    { href: '/sign-out', label: 'Sign out' },
  ]
</script>