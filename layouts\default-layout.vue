<template>
  <div>
    <nav class="bg-gray-100 p-4 mb-8">
      <Menu>
        <MenuButton class="bg-blue-500 text-white px-4 py-2 rounded">
          Options
        </MenuButton>
        <MenuItems class="absolute mt-2 bg-white border rounded shadow-lg">
          <MenuItem
            v-for="link in links"
            :key="link.href"
            :href="link.href"
            as="a"
            class="block px-4 py-2 hover:bg-blue-500 hover:text-white"
          >
            {{ link.label }}
          </MenuItem>
        </MenuItems>
      </Menu>
    </nav>

    <!-- This is where the page content will be rendered -->
    <main class="container mx-auto px-4">
      <slot />
    </main>
  </div>
</template>

<script setup>
  import { Menu, MenuButton, MenuItems, MenuItem } from '@headlessui/vue'

  const links = [
    { href: '/', label: 'Home' },
    { href: '/support', label: 'Support' },
    { href: '/license', label: 'License' },
    { href: '/sign-out', label: 'Sign out' },
  ]
</script>